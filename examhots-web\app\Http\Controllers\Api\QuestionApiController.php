<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Answer;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class QuestionApiController extends Controller
{
    /**
     * Sanitize HTML content by removing all HTML tags
     */
    private function sanitizeHtml($content)
    {
        if (empty($content)) {
            return $content;
        }

        // Remove all HTML tags and decode HTML entities
        $sanitized = strip_tags($content);
        $sanitized = html_entity_decode($sanitized, ENT_QUOTES, 'UTF-8');

        // Trim whitespace
        return trim($sanitized);
    }

    /**
     * Create new question
     */
    public function store(Request $request, $materialId)
    {
        try {

            $validator = Validator::make($request->all(), [
                'question' => 'required|string',
                'type' => 'required|in:pilihan_ganda,uraian_singkat,esai',
                'images' => 'nullable|array',
                'images.*' => 'image|mimes:jpeg,jpg,png|max:5120', // 5MB max
            ]);

            if ($validator->fails()) {
                // Create specific error message based on validation errors
                $errorMessages = [];
                foreach ($validator->errors()->all() as $message) {
                    $errorMessages[] = $message;
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                    'errors' => $validator->errors()
                ], 422);
            }

            $type = $request->type;

            // Validate based on question type
            if ($type === 'pilihan_ganda') {
                $validator = Validator::make($request->all(), [
                    'answers' => 'required|array|min:2|max:6',
                    'answers.*.text' => 'required|string',
                    'correct_answer_index' => 'required|integer|min:0',
                ]);
            } elseif ($type === 'uraian_singkat') {
                $validator = Validator::make($request->all(), [
                    'answer' => 'required|string',
                ]);
            } elseif ($type === 'esai') {
                $validator = Validator::make($request->all(), [
                    'answer' => 'required|string',
                    'score' => 'nullable|numeric|min:0',
                ]);
            }

            if ($validator->fails()) {
                // Create specific error message based on validation errors
                $errorMessages = [];
                foreach ($validator->errors()->all() as $message) {
                    $errorMessages[] = $message;
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                    'errors' => $validator->errors()
                ], 422);
            }

            // Handle image uploads
            $uploadedImages = [];
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    $filename = hash('sha256', time() . '_' . uniqid()) . '.' . $image->getClientOriginalExtension();

                    // Use move() method instead of storeAs to avoid finfo dependency
                    $uploadPath = public_path('storage/uploads/images/question/');
                    if (!file_exists($uploadPath)) {
                        mkdir($uploadPath, 0755, true);
                    }
                    $image->move($uploadPath, $filename);

                    $uploadedImages[] = $filename;
                }
            }

            // Create question
            $question = new Question();
            $question->questionmaterialid = $materialId;
            $question->question = $this->sanitizeHtml($request->question);
            $question->type = $type;
            $question->img = !empty($uploadedImages) ? implode(',', $uploadedImages) : null;
            $question->save();

            // Create answers based on type
            if ($type === 'pilihan_ganda') {
                $correctAnswerIndex = (int) $request->input('correct_answer_index', 0);

                // Handle answers - check if it's JSON array or multipart form data
                $answers = $request->input('answers', []);
                $answerTexts = [];

                if (is_array($answers) && !empty($answers)) {
                    // Handle JSON array format from Flutter
                    foreach ($answers as $index => $answerData) {
                        if (is_array($answerData) && isset($answerData['text'])) {
                            $answerTexts[$index] = $answerData['text'];
                        }
                    }
                } else {
                    // Handle multipart form data format (answers[0][text], answers[1][text], etc.)
                    $allInputs = $request->all();
                    foreach ($allInputs as $key => $value) {
                        if (preg_match('/^answers\[(\d+)\]\[text\]$/', $key, $matches)) {
                            $index = (int) $matches[1];
                            $answerTexts[$index] = $value;
                        }
                    }
                }

                // Sort by index and create answers
                ksort($answerTexts);
                foreach ($answerTexts as $index => $answerText) {
                    // Create answer even if empty to maintain consistency
                    Answer::create([
                        'questionid' => $question->id,
                        'answer' => $this->sanitizeHtml(trim($answerText)),
                        'is_correct' => $index == $correctAnswerIndex ? 1 : 0,
                    ]);
                }
            } else {
                // For uraian_singkat and esai
                $answer = $request->input('answer', '');
                $score = $request->input('score');

                if (!empty(trim($answer))) {
                    $answerData = [
                        'questionid' => $question->id,
                        'answer' => $this->sanitizeHtml(trim($answer)),
                        'is_correct' => 1,
                    ];

                    // Add score for essay questions
                    if ($question->type === 'esai' && $score !== null) {
                        $answerData['score'] = intval($score);
                    }

                    Answer::create($answerData);
                }
            }

            // Load question with answers for response
            $question->load('answers');

            return response()->json([
                'success' => true,
                'message' => 'Question created successfully',
                'data' => $question
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create question',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload single image
     */
    public function uploadImage(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => 'required|image|mimes:jpeg,jpg,png|max:5120', // 5MB max
            ]);

            if ($validator->fails()) {
                // Create specific error message based on validation errors
                $errorMessages = [];
                foreach ($validator->errors()->all() as $message) {
                    $errorMessages[] = $message;
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                    'errors' => $validator->errors()
                ], 422);
            }

            $file = $request->file('file');
            $filename = hash('sha256', time() . '_' . uniqid()) . '.' . $file->getClientOriginalExtension();

            // Use move() method instead of storeAs to avoid finfo dependency
            $uploadPath = public_path('storage/uploads/images/question/');
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            $file->move($uploadPath, $filename);

            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'data' => [
                    'filename' => $filename,
                    'url' => '/storage/uploads/images/question/' . $filename
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update question
     */
    public function update(Request $request, $id)
    {
        Log::info('Update Question Called', [
            'id' => $id,
            'method' => $request->method(),
            'all_data' => $request->all()
        ]);

        try {
            $question = Question::find($id);

            if (!$question) {
                return response()->json([
                    'success' => false,
                    'message' => 'Question not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'question' => 'required|string',
                'type' => 'required|in:pilihan_ganda,uraian_singkat,esai',
                'images' => 'nullable|array',
                'images.*' => 'image|mimes:jpeg,jpg,png|max:5120', // 5MB max
            ]);

            if ($validator->fails()) {
                // Create specific error message based on validation errors
                $errorMessages = [];
                foreach ($validator->errors()->all() as $message) {
                    $errorMessages[] = $message;
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                    'errors' => $validator->errors()
                ], 422);
            }

            // Handle image uploads
            $uploadedImages = [];
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    $filename = hash('sha256', time() . '_' . uniqid()) . '.' . $image->getClientOriginalExtension();

                    // Use move() method instead of storeAs to avoid finfo dependency
                    $uploadPath = public_path('storage/uploads/images/question/');
                    if (!file_exists($uploadPath)) {
                        mkdir($uploadPath, 0755, true);
                    }
                    $image->move($uploadPath, $filename);

                    $uploadedImages[] = $filename;
                }
            }

            // Handle existing images (from Flutter app)
            $existingImages = [];
            if ($request->has('existing_images') && !empty($request->existing_images)) {
                $existingImages = array_filter(explode(',', $request->existing_images));
            }

            // Determine which old images to delete
            $oldImages = !empty($question->img) ? explode(',', $question->img) : [];
            $imagesToDelete = array_diff($oldImages, $existingImages);

            // Delete removed images
            foreach ($imagesToDelete as $image) {
                $image = trim($image);
                if (!empty($image)) {
                    $filePath = public_path('storage/uploads/images/question/' . $image);
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                }
            }

            // Combine existing images with newly uploaded images
            $allImages = array_merge($existingImages, $uploadedImages);
            $finalImages = array_filter($allImages); // Remove empty values

            // Update question
            $question->question = $this->sanitizeHtml($request->question);
            $question->type = $request->type;
            $question->img = !empty($finalImages) ? implode(',', $finalImages) : null;
            $question->save();

            // Delete existing answers
            Answer::where('questionid', $question->id)->delete();

            // Handle answers based on question type
            if ($request->type === 'pilihan_ganda') {
                $correctAnswerIndex = (int) $request->input('correct_answer_index', 0);
                $answers = $request->input('answers', []);

                Log::info('Processing Pilihan Ganda Answers:', [
                    'answers' => $answers,
                    'correct_index' => $correctAnswerIndex,
                    'is_array' => is_array($answers)
                ]);

                // Process answers directly from JSON array
                if (is_array($answers)) {
                    foreach ($answers as $index => $answerData) {
                        $answerText = '';

                        if (is_array($answerData) && isset($answerData['text'])) {
                            $answerText = $answerData['text'];
                        } elseif (is_string($answerData)) {
                            $answerText = $answerData;
                        }

                        $created = Answer::create([
                            'questionid' => $question->id,
                            'answer' => $this->sanitizeHtml(trim($answerText)),
                            'is_correct' => $index == $correctAnswerIndex ? 1 : 0,
                        ]);

                        Log::info('Created Answer:', [
                            'index' => $index,
                            'text' => $answerText,
                            'is_correct' => $index == $correctAnswerIndex,
                            'answer_id' => $created->id
                        ]);
                    }
                }
            } else {
                // For uraian_singkat and esai
                $answer = $request->input('answer', '');
                $score = $request->input('score');

                if (!empty(trim($answer))) {
                    $answerData = [
                        'questionid' => $question->id,
                        'answer' => $this->sanitizeHtml(trim($answer)),
                        'is_correct' => 1,
                    ];

                    // Add score for essay questions
                    if ($question->type === 'esai' && $score !== null) {
                        $answerData['score'] = intval($score);
                    }

                    // Update existing answer or create new one
                    $existingAnswer = Answer::where('questionid', $question->id)->first();
                    if ($existingAnswer) {
                        $existingAnswer->update($answerData);
                        Log::info('Updated Answer:', $existingAnswer->toArray());
                    } else {
                        $created = Answer::create($answerData);
                        Log::info('Created Answer:', $created->toArray());
                    }
                }
            }

            // Load question with answers for response
            $question->load('answers');

            return response()->json([
                'success' => true,
                'message' => 'Question updated successfully',
                'data' => $question
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update question',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete question
     */
    public function destroy($id)
    {
        try {
            $question = Question::find($id);

            if (!$question) {
                return response()->json([
                    'success' => false,
                    'message' => 'Question not found'
                ], 404);
            }

            // Delete associated images
            if (!empty($question->img)) {
                $images = explode(',', $question->img);
                foreach ($images as $image) {
                    $filePath = public_path('storage/uploads/images/question/' . $image);
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                }
            }

            // Delete answers
            Answer::where('questionid', $question->id)->delete();

            // Delete question
            $question->delete();

            return response()->json([
                'success' => true,
                'message' => 'Question deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete question',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
