@extends('main')

@section('css')
    <link rel="stylesheet" href="{{ asset('css/management-style.css') }}">
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header flex items-center justify-between mb-8">
        <div class="flex items-center space-x-3">
            <svg class="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h1 class="page-title text-3xl font-bold text-gray-800">J<PERSON><PERSON></h1>
        </div>

        <div class="page-header-actions flex items-center space-x-4">
            <!-- Search Bar -->
            <div class="search-container relative">
                <input type="text" id="searchInput" placeholder="<PERSON><PERSON> j<PERSON>wal ujian di sini..."
                    class="search-input w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent">
                <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>

            <!-- Add Button -->
            <button onclick="openCreateModal()"
                class="btn-primary bg-primary-blue text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 hover:shadow-lg hover:scale-105 transition-all duration-200 ease-in-out">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                <span>Tambah Jadwal Ujian</span>
            </button>
        </div>
    </div>

    <!-- Cards Grid -->
    <div class="cards-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="cardsContainer">
        @forelse ($exam as $examItem)
            <div class="card-hover bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow exam-card"
                data-name="{{ strtolower($examItem->name) }}"
                data-subject="{{ strtolower($examItem->questionmaterial->name ?? '') }}"
                data-class="{{ strtolower($examItem->class->name ?? '') }}"
                data-token="{{ strtolower($examItem->token ?? '') }}">

                <!-- Card Header -->
                <div class="flex justify-between items-start mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-800 line-clamp-2 mb-2">{{ $examItem->name }}</h3>
                        <div class="flex items-center space-x-2">
                            @php
                                $now = now();
                                $status = 'Fleksibel';
                                $statusColor = 'bg-blue-100 text-blue-800';

                                // Check if exam has complete schedule data
                                if (
                                    !empty($examItem->startdate) &&
                                    !empty($examItem->enddate) &&
                                    !empty($examItem->starttime) &&
                                    !empty($examItem->endtime) &&
                                    $examItem->startdate !== null &&
                                    $examItem->enddate !== null &&
                                    $examItem->starttime !== null &&
                                    $examItem->endtime !== null
                                ) {
                                    try {
                                        $startDateTime = \Carbon\Carbon::createFromFormat(
                                            'Y-m-d H:i:s',
                                            $examItem->startdate . ' ' . $examItem->starttime,
                                            config('app.timezone'),
                                        );
                                        $endDateTime = \Carbon\Carbon::createFromFormat(
                                            'Y-m-d H:i:s',
                                            $examItem->enddate . ' ' . $examItem->endtime,
                                            config('app.timezone'),
                                        );

                                        if ($now->between($startDateTime, $endDateTime)) {
                                            $status = 'Berlangsung';
                                            $statusColor = 'bg-green-100 text-green-800';
                                        } elseif ($now->lt($startDateTime)) {
                                            $status = 'Akan Datang';
                                            $statusColor = 'bg-yellow-100 text-yellow-800';
                                        } else {
                                            $status = 'Selesai';
                                            $statusColor = 'bg-gray-100 text-gray-800';
                                        }
                                    } catch (\Exception $e) {
                                        // If date parsing fails, default to flexible
                                        $status = 'Fleksibel';
                                        $statusColor = 'bg-blue-100 text-blue-800';
                                    }
                                }
                            @endphp
                            <span class="px-2 py-1 rounded-full text-xs font-medium {{ $statusColor }}">
                                {{ $status }}
                            </span>
                        </div>
                    </div>

                    <!-- Dropdown Menu -->
                    <div class="relative ml-4">
                        <button
                            class="dropdown-toggle text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
                            data-dropdown="dropdown-{{ $examItem->id }}">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                                </path>
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="dropdown-{{ $examItem->id }}"
                            class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10 hidden">
                            <div class="py-1">
                                <a href="{{ route('exam.edit', $examItem->id) }}"
                                    onclick="openEditModal(event, this.href); return false;"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                                    <svg class="w-4 h-4 mr-3 text-blue-500" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                    Edit Ujian
                                </a>
                                <a href="{{ route('exam.question.tabel', $examItem->id) }}"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                                    <svg class="w-4 h-4 mr-3 text-orange-500" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                        </path>
                                    </svg>
                                    Lihat Soal
                                </a>
                                <a href="{{ route('exam.student', $examItem->id) }}"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                                    <svg class="w-4 h-4 mr-3 text-green-500" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                                        </path>
                                    </svg>
                                    Daftar Peserta
                                </a>
                                <button onclick="deleteExam({{ $examItem->id }}, '{{ $examItem->name }}')"
                                    class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                    Hapus Ujian
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card Content -->
                <div class="space-y-3">
                    <!-- Date Range -->
                    @if (
                        !empty($examItem->startdate) &&
                            !empty($examItem->enddate) &&
                            $examItem->startdate !== null &&
                            $examItem->enddate !== null)
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                </path>
                            </svg>
                            <span>
                                @try
                                    @if ($examItem->startdate === $examItem->enddate)
                                        {{ \Carbon\Carbon::parse($examItem->startdate)->format('d M Y') }}
                                    @else
                                        {{ \Carbon\Carbon::parse($examItem->startdate)->format('d M') }} -
                                        {{ \Carbon\Carbon::parse($examItem->enddate)->format('d M Y') }}
                                    @endif
                                    @catch (\Exception $e)
                                    {{ $examItem->startdate }} - {{ $examItem->enddate }}
                                @endtry
                            </span>
                        </div>
                    @else
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                </path>
                            </svg>
                            <span>Ujian Fleksibel</span>
                        </div>
                    @endif

                    <!-- Time Range -->
                    @if (
                        !empty($examItem->starttime) &&
                            !empty($examItem->endtime) &&
                            $examItem->starttime !== null &&
                            $examItem->endtime !== null)
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ $examItem->starttime }} - {{ $examItem->endtime }}</span>
                        </div>
                    @else
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ $examItem->duration }} menit</span>
                        </div>
                    @endif

                    <!-- Bank Soal -->
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                            </path>
                        </svg>
                        <span>{{ $examItem->questionmaterial->name ?? '-' }}</span>
                    </div>

                    <!-- Class -->
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                        <span>Kelas {{ $examItem->class->name ?? '-' }}</span>
                    </div>

                    <!-- Creator (only show for admin) -->
                    @if (Auth::user()->role === 'admin')
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                </path>
                            </svg>
                            <span>Dibuat oleh: {{ $examItem->teacher->name ?? 'Admin' }}</span>
                        </div>
                    @endif

                    <!-- Duration & Questions -->
                    <div class="flex items-center justify-between text-sm text-gray-600">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ $examItem->duration }} menit</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                            <span>{{ $examItem->amountquestion ?? 0 }} soal</span>
                        </div>
                    </div>

                    <!-- Token -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Token Ujian:</span>
                        <span class="px-3 py-1 bg-primary-blue text-white text-sm font-mono rounded-md">
                            {{ $examItem->token ?? '-' }}
                        </span>
                    </div>
                </div>

                <!-- Card Footer -->
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-500">ID: {{ $examItem->id }}</span>
                        <div class="flex space-x-2">
                            <a href="{{ route('exam.edit', $examItem->id) }}"
                                onclick="openEditModal(event, this.href); return false;"
                                class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors">
                                Edit
                            </a>
                            <a href="{{ route('exam.student', $examItem->id) }}"
                                class="text-green-600 hover:text-green-800 text-sm font-medium transition-colors">
                                Peserta
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <!-- Empty State -->
            <div class="col-span-full flex flex-col items-center justify-center py-12">
                <svg class="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4m-6 0h6m-6 0V7a1 1 0 00-1 1v9a2 2 0 002 2h4a2 2 0 002-2V8a1 1 0 00-1-1V7">
                    </path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Belum ada jadwal ujian</h3>
                <p class="text-gray-500 mb-6">Mulai dengan membuat jadwal ujian pertama Anda</p>
                <button onclick="openCreateModal()"
                    class="bg-primary-blue text-white px-6 py-3 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 transition-all">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Tambah Jadwal Ujian</span>
                </button>
            </div>
        @endforelse
    </div>

    <!-- Hidden form for delete -->
    <form id="deleteForm" method="POST" style="display: none;">
        @csrf
        @method('DELETE')
    </form>

    <!-- Modal Global -->
    <div id="modalGlobal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div id="modalContent"
            class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-200">
            <!-- Modal content will be loaded here -->
        </div>
    </div>
@endsection

@section('script')
    <script src="{{ asset('package/dist/libs/sweetalert2/dist/sweetalert2.min.js') }}"></script>
    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const cards = document.querySelectorAll('.exam-card');

            cards.forEach(card => {
                const name = card.getAttribute('data-name');
                const subject = card.getAttribute('data-subject');
                const className = card.getAttribute('data-class');
                const token = card.getAttribute('data-token');

                if (name.includes(searchTerm) || subject.includes(searchTerm) ||
                    className.includes(searchTerm) || token.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // Dropdown functionality
        document.addEventListener('click', function(e) {
            // Close all dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });

            // Open clicked dropdown
            if (e.target.closest('.dropdown-toggle')) {
                e.preventDefault();
                const button = e.target.closest('.dropdown-toggle');
                const dropdownId = button.getAttribute('data-dropdown');
                const dropdown = document.getElementById(dropdownId);
                dropdown.classList.toggle('hidden');
            }
        });

        // Modal functions
        function openCreateModal() {
            console.log('Opening create modal');

            fetch("{{ route('exam.add') }}")
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    console.log('Modal content loaded successfully');
                    const modal = document.getElementById('modalGlobal');
                    const content = document.getElementById('modalContent');

                    if (!modal || !content) {
                        console.error('Modal elements not found');
                        return;
                    }

                    content.innerHTML = html;
                    modal.classList.remove('hidden');

                    // Animation
                    setTimeout(() => {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }, 50);

                    // Execute scripts in the loaded content
                    const scripts = content.querySelectorAll('script');
                    scripts.forEach(script => {
                        const newScript = document.createElement('script');
                        if (script.src) {
                            newScript.src = script.src;
                        } else {
                            newScript.textContent = script.textContent;
                        }
                        document.head.appendChild(newScript);
                        document.head.removeChild(newScript);
                    });

                    setupModalListeners();
                })
                .catch(error => {
                    console.error('Gagal membuka modal:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Gagal membuka form tambah jadwal ujian. Silakan coba lagi.',
                        confirmButtonColor: '#455A9D'
                    });
                });
        }

        function openEditModal(event, url) {
            event.preventDefault();
            event.stopPropagation();

            console.log('Opening edit modal for URL:', url);

            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    console.log('Modal content loaded successfully');
                    const modal = document.getElementById('modalGlobal');
                    const content = document.getElementById('modalContent');

                    if (!modal || !content) {
                        console.error('Modal elements not found');
                        return;
                    }

                    content.innerHTML = html;
                    modal.classList.remove('hidden');

                    // Animation
                    setTimeout(() => {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }, 50);

                    // Execute scripts in the loaded content
                    const scripts = content.querySelectorAll('script');
                    scripts.forEach(script => {
                        const newScript = document.createElement('script');
                        if (script.src) {
                            newScript.src = script.src;
                        } else {
                            newScript.textContent = script.textContent;
                        }
                        document.head.appendChild(newScript);
                        document.head.removeChild(newScript);
                    });

                    setupModalListeners();
                })
                .catch(error => {
                    console.error('Gagal membuka modal edit:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Gagal membuka form edit. Silakan coba lagi.',
                        confirmButtonColor: '#455A9D'
                    });
                });
        }

        function setupModalListeners() {
            // Focus on first input
            const firstInput = document.querySelector('#modalContent input[type="text"]');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }

            // Form validation
            const form = document.querySelector('#modalContent form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const nameField = form.querySelector('input[name="name"]');

                    if (nameField && nameField.value.trim() === '') {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Peringatan!',
                            text: 'Nama ujian harus diisi!',
                            confirmButtonColor: '#455A9D'
                        });
                        nameField.focus();
                        return;
                    }
                });
            }
        }

        function closeModal() {
            const modal = document.getElementById('modalGlobal');
            const content = document.getElementById('modalContent');

            content.classList.remove('scale-100', 'opacity-100');
            content.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                modal.classList.add('hidden');
                content.innerHTML = '';
            }, 200);
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('modalGlobal');
            if (e.target === modal) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('modalGlobal');
                if (!modal.classList.contains('hidden')) {
                    closeModal();
                }
            }
        });

        // Delete function
        function deleteExam(id, name) {
            Swal.fire({
                title: 'Apakah Anda yakin?',
                text: `Jadwal ujian "${name}" akan dihapus permanen!`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Ya, hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.getElementById('deleteForm');
                    form.action = `/exam/schedule/delete/${id}`;
                    form.submit();
                }
            });
        }

        // Show success/error messages
        @if (session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                timer: 3000,
                showConfirmButton: false
            });
        @endif

        @if (session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: '{{ session('error') }}',
                timer: 3000,
                showConfirmButton: false
            });
        @endif

        @if ($errors->any())
            let errorMessages = '';
            @foreach ($errors->all() as $error)
                errorMessages += '• {!! addslashes(str_replace(["\r\n", "\r", "\n"], "\\n", $error)) !!}\n';
            @endforeach

            Swal.fire({
                icon: 'error',
                title: 'Validasi Gagal!',
                html: '<div style="text-align: left; white-space: pre-line;">' + errorMessages + '</div>',
                timer: 8000,
                showConfirmButton: true,
                confirmButtonText: 'OK'
            });
        @endif
    </script>
@endsection
