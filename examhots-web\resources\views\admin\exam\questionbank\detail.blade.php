@extends('main')

@section('content')
    <div class="flex items-center justify-between mb-8">
        <div class="flex items-center space-x-4">
            <!-- Back Button -->
            <a href="{{ route('question.index') }}" class="text-gray-600 hover:text-gray-800 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7">
                    </path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-gray-800">Detail Bank Soal</h1>
        </div>

        <div class="flex items-center space-x-4">
            <!-- Search Bar -->
            <div class="relative">
                <input type="text" id="searchInput" placeholder="Cari soal di sini..."
                    class="w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent">
                <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>

            <!-- Add Question Button -->
            <a href="javascript:;" onclick="openChoosteTypeModal({{ $questionmaterial->id }})"
                class="bg-primary-blue text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 hover:shadow-lg hover:scale-105 transition-all duration-200 ease-in-out">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                <span>Tambahkan Soal Baru</span>
            </a>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <h2 class="text-2xl font-semibold text-gray-900 mb-2">{{ $questionmaterial->name }}</h2>
                <p class="text-gray-600">
                    {{ $questionmaterial->description }}
                </p>
            </div>
            <!-- Edit Button -->
            <button onclick="openEditBankSoalModal({{ $questionmaterial->id }})"
                class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                    </path>
                </svg>
            </button>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm mb-8">
        <div class="border-b border-gray-200">
            <nav class="flex space-x-8 px-6">
                @php
                    $multipleChoice = $questions->where('type', 'pilihan_ganda')->count();
                    $shortAnswer = $questions->where('type', 'uraian_singkat')->count();
                    $essay = $questions->where('type', 'esai')->count();
                @endphp

                <button id="tab-multiple-choice-tab"
                    class="tab-button py-4 px-1 border-b-2 border-primary-blue text-primary-blue font-medium text-sm transition-colors"
                    data-tab="pilihan-ganda">
                    Pilihan Ganda
                    <span
                        class="tab-badge ml-2 {{ $multipleChoice > 0 ? 'bg-primary-blue text-white' : 'bg-gray-200 text-gray-600' }} text-xs px-2 py-1 rounded-full">
                        {{ $multipleChoice }}
                    </span>
                </button>

                <button id="tab-short-description-tab"
                    class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm transition-colors"
                    data-tab="uraian-singkat">
                    Uraian Singkat
                    <span
                        class="tab-badge ml-2 {{ $shortAnswer > 0 ? 'bg-primary-blue text-white' : 'bg-gray-200 text-gray-600' }} text-xs px-2 py-1 rounded-full">
                        {{ $shortAnswer }}
                    </span>
                </button>

                <button id="tab-essay-tab"
                    class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm transition-colors"
                    data-tab="essay">
                    Essay
                    <span
                        class="tab-badge ml-2 {{ $essay > 0 ? 'bg-primary-blue text-white' : 'bg-gray-200 text-gray-600' }} text-xs px-2 py-1 rounded-full">
                        {{ $essay }}
                    </span>
                </button>
            </nav>
        </div>
    </div>

    <div class="tab-content">
        <!-- Pilihan Ganda Content -->
        <div id="tab-pilihan-ganda" class="tab-pane active">
            <!-- Score Management for Pilihan Ganda -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Skor Pilihan Ganda</h3>

                <form id="pgScoreForm" action="{{ route('question.material.update.scores', $questionmaterial->id) }}"
                    method="POST">
                    @csrf
                    @method('PUT')

                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <label for="pg_total_score" class="block text-sm font-medium text-gray-700 mb-2">
                                Total Skor Pilihan Ganda
                            </label>
                            <div class="relative">
                                <input type="number" id="pg_total_score" name="pg_total_score"
                                    value="{{ old('pg_total_score', $questionmaterial->pg_total_score ?? $pg_questions->count() * 10) }}"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="e.g. 100" min="0">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 text-sm">pts</span>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">{{ $pg_questions->count() }} soal pilihan ganda</p>
                        </div>
                    </div>

                    <div class="mt-6">
                        <button type="submit"
                            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Simpan Skor
                        </button>
                    </div>
                </form>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-12 text-center">
                <div class="w-full">
                    @isset($pg_questions)
                        @include('admin.exam.questionbank.components.pilihan_ganda', [
                            'questions' => $pg_questions,
                        ])
                    @endisset
                </div>
            </div>
        </div>

        <div id="tab-uraian-singkat" class="tab-pane">
            <!-- Score Management for Uraian Singkat -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Skor Uraian Singkat</h3>

                <form id="uraianScoreForm" action="{{ route('question.material.update.scores', $questionmaterial->id) }}"
                    method="POST">
                    @csrf
                    @method('PUT')

                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <label for="uraian_total_score" class="block text-sm font-medium text-gray-700 mb-2">
                                Total Skor Uraian Singkat
                            </label>
                            <div class="relative">
                                <input type="number" id="uraian_total_score" name="uraian_total_score"
                                    value="{{ old('uraian_total_score', $questionmaterial->uraian_total_score ?? $urai_questions->count() * 15) }}"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="e.g. 100" min="0">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 text-sm">pts</span>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">{{ $urai_questions->count() }} soal uraian singkat</p>
                        </div>
                    </div>

                    <div class="mt-6">
                        <button type="submit"
                            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Simpan Skor
                        </button>
                    </div>
                </form>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-12 text-center">
                <div class="w-full">
                    @isset($urai_questions)
                        @include('admin.exam.questionbank.components.uraian_singkat', [
                            'questions' => $urai_questions,
                        ])
                    @endisset
                </div>
            </div>
        </div>

        <div id="tab-essay" class="tab-pane">
            <div class="bg-white rounded-lg shadow-sm p-12 text-center">
                <div class="w-full">
                    @isset($urai_questions)
                        @include('admin.exam.questionbank.components.esai', [
                            'questions' => $esai_questions,
                        ])
                    @endisset
                </div>
            </div>
        </div>
    </div>



    {{-- <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h5 class="card-title fw-semibold">{{ $questionmaterial->name }}</h5>
                            <span class="card-subtitle fw-light">{{ $questionmaterial->description }}</span>
                        </div>


                        <div class="col-6 text-end gap-3">
                            <a href="{{ route('question.index') }}" class="btn btn-danger">
                                <i class="ti ti-arrow-left"></i> Kembali
                            </a>
                            <a href="{{ route('question.detail.add', $questionmaterial->id) }}" class="btn btn-primary">
                                <i class="ti ti-plus"></i> Tambah Soal Baru
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> --}}

    {{-- <div class="row">
        <div class="col-12">
            {{-- <ul class="nav nav-pills mb-3" role="tablist"> --}}
    {{-- <li class="nav-item">
                    <button type="button" class="nav-link active" id="tab-multiple-choice-tab" data-bs-toggle="tab"
                        data-bs-target="#tab-multiple-choice" aria-selected="true">Pilihan Ganda</button>
                </li>
                <li class="nav-item">
                    <button type="button" class="nav-link" id="tab-short-description-tab" data-bs-toggle="tab"
                        data-bs-target="#tab-short-description" aria-selected="false">Uraian Singkat</button>
                </li>
                <li class="nav-item">
                    <button type="button" class="nav-link" id="tab-essay-tab" data-bs-toggle="tab"
                        data-bs-target="#tab-essay" aria-selected="false">Esai</button>
                </li>
            </ul> --}}


    {{-- <div class="tab-content"> --}}
    {{-- Multiple Choice --}}
    {{-- <div class="tab-pane fade show active" id="tab-multiple-choice" role="tabpanel">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h5>Soal Pilihan Ganda</h5>
                                </div>

                                @include('admin.exam.questionbank.components.pilihan_ganda', [
                                    'questions' => $pg_questions,
                                ])
                            </div>
                        </div>
                    </div> --}}

    {{-- Short Description --}}
    {{-- <div class="tab-pane fade" id="tab-uraian-singkat" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between mb-4">
                                <h5>Soal Uraian Singkat</h5>
                            </div>

                            @include('admin.exam.questionbank.components.uraian_singkat', [
                                'questions' => $urai_questions,
                            ])
                        </div>
                    </div>
                </div> --}}

    {{-- Essay --}}
    {{-- <div class="tab-pane fade" id="tab-essay" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between mb-4">
                                <h5>Soal Essay</h5>
                            </div>

                            @include('admin.exam.questionbank.components.esai', [
                                'questions' => $esai_questions,
                            ])
                        </div>
                    </div>
                </div> --}}
    {{-- </div>
        </div> --}}
    </div>
@endsection

@section('script')
    <script src="{{ asset('package/dist/libs/sweetalert2/dist/sweetalert2.min.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll(".tab-button");
            const tabPanes = document.querySelectorAll(".tab-pane");

            // Ambil tab dari URL atau localStorage
            const tabParam = new URLSearchParams(window.location.search).get("tab");
            const savedTab = tabParam || localStorage.getItem("activeTab") || "pilihan-ganda";

            function activateTab(tabName) {
                // Update tombol
                tabButtons.forEach(btn => {
                    const tab = btn.getAttribute("data-tab");
                    if (tab === tabName) {
                        btn.classList.add("border-primary-blue", "text-primary-blue");
                        btn.classList.remove("border-transparent", "text-gray-500");
                    } else {
                        btn.classList.remove("border-primary-blue", "text-primary-blue");
                        btn.classList.add("border-transparent", "text-gray-500");
                    }
                });

                // Update konten
                tabPanes.forEach(pane => {
                    if (pane.id === `tab-${tabName}`) {
                        pane.classList.remove("hidden");
                        pane.classList.add("active");
                    } else {
                        pane.classList.add("hidden");
                        pane.classList.remove("active");
                    }
                });

                // Simpan state
                localStorage.setItem("activeTab", tabName);
                const newUrl = new URL(window.location);
                newUrl.searchParams.set('tab', tabName);
                history.replaceState(null, '', newUrl);
            }

            // Aktifkan tab awal saat load
            activateTab(savedTab);

            // Listener klik tab
            tabButtons.forEach(btn => {
                btn.addEventListener("click", () => {
                    const targetTab = btn.getAttribute("data-tab");
                    activateTab(targetTab);
                });
            });
        });

        // Function to submit score forms
        function submitScoreForm(form, scoreType) {
            const formData = new FormData(form);
            const submitButton = form.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Show loading state
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="ti ti-loader animate-spin mr-2"></i>Menyimpan...';

            fetch(form.action, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json',
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: scoreType + ' berhasil diperbarui!',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: data.message || 'Gagal memperbarui skor',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menyimpan skor',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                })
                .finally(() => {
                    // Reset button state
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                });
        }

        // Handler untuk form score submission
        document.addEventListener('submit', function(e) {
            const form = e.target;

            // Handle score forms
            if (form.id === 'pgScoreForm') {
                e.preventDefault();
                submitScoreForm(form, 'Skor pilihan ganda');
                return;
            }

            if (form.id === 'uraianScoreForm') {
                e.preventDefault();
                submitScoreForm(form, 'Skor uraian singkat');
                return;
            }

            if (form.id === 'chooseTypeForm') {
                e.preventDefault();

                const formData = new FormData(form);

                fetch(form.action, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}',
                            'Accept': 'application/json',
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Tutup modal lama
                            const modal = document.getElementById('modalGlobal');
                            const content = document.getElementById('modalContent');

                            content.classList.remove('scale-100', 'opacity-100');
                            content.classList.add('scale-95', 'opacity-0');

                            setTimeout(() => {
                                modal.classList.add('hidden');
                                content.innerHTML = '';
                            }, 200);

                            // Tampilkan modal baru (gunakan modalGlobal yang sama)
                            setTimeout(() => {
                                content.innerHTML = data.html;
                                modal.classList.remove('hidden');
                                modal.classList.add('flex');

                                // Delay animasi biar smooth muncul
                                setTimeout(() => {
                                    content.classList.remove('scale-95', 'opacity-0');
                                    content.classList.add('scale-100', 'opacity-100');

                                    initDropzone();

                                    // Initialize Quill editors if they exist in the modal
                                    if (typeof window.initializeQuillEditors === 'function') {
                                        setTimeout(() => {
                                            window.initializeQuillEditors();
                                        }, 300);
                                    }

                                    // Re-setup modal close handlers specifically for new content
                                    setupModalCloseHandlersForNewContent();
                                }, 50);
                            }, 200);

                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Gagal!',
                                text: data.message,
                                confirmButtonColor: '#455A9D'
                            });
                        }
                    })
                    .catch(error => {
                        console.error("AJAX Error:", error);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat submit.',
                            confirmButtonColor: '#455A9D'
                        });
                    });
            }
        });


        function openChoosteTypeModal(questionmaterialid) {
            fetch("{{ route('question.detail.add', $questionmaterial->id) }}")
                .then(response => response.text())
                .then(html => {
                    const modal = document.getElementById('modalGlobal');
                    const content = document.getElementById('modalContent');

                    content.innerHTML = html;
                    modal.classList.remove('hidden');

                    // Biar animasinya muncul (delay dikit)
                    setTimeout(() => {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }, 50);

                    setupModalListeners();
                    setupModalCloseHandlers();
                })
                .catch(error => {
                    console.error('Gagal membuka modal:', error);
                });
        }

        function setupModalListeners() {
            const questionTypeRadios = document.querySelectorAll('.question-type-radio');
            const continueQuestionType = document.getElementById('continueQuestionType'); // pastikan ID ini ada!

            // Note: Quill editors are initialized when second modal (question form) is loaded

            // Character count functionality for bank soal edit form
            const textarea = document.getElementById('description');
            const charCount = document.getElementById('charCount');

            if (textarea && charCount) {
                textarea.addEventListener('input', () => {
                    const count = textarea.value.length;
                    charCount.textContent = `${count}/200`;

                    if (count > 200) {
                        charCount.classList.add('text-red-500');
                    } else {
                        charCount.classList.remove('text-red-500');
                    }
                });

                // Trigger initial update when modal is opened
                charCount.textContent = `${textarea.value.length}/200`;
            }

            questionTypeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    // Reset all labels
                    document.querySelectorAll('label').forEach(label => {
                        label.classList.remove('border-primary-blue', 'bg-blue-50');
                        label.classList.add('border-gray-200');
                    });

                    // Hide all radio dots
                    document.querySelectorAll('.radio-dot').forEach(dot => {
                        dot.classList.add('hidden');
                    });

                    // Highlight selected option
                    if (this.checked) {
                        const parentLabel = this.closest('label');
                        parentLabel.classList.remove('border-gray-200');
                        parentLabel.classList.add('border-primary-blue', 'bg-blue-50');

                        // Show radio dot for selected option
                        const radioDot = parentLabel.querySelector('.radio-dot');
                        if (radioDot) {
                            radioDot.classList.remove('hidden');
                        }

                        // Enable continue button
                        if (continueQuestionType) {
                            continueQuestionType.disabled = false;
                        }
                    }
                });
            });

            // Handle form submission for question type selection
            // Look for form specifically in the modal content
            const modalContent = document.getElementById('modalContent');
            const questionTypeForm = modalContent ? modalContent.querySelector('form') : null;

            if (questionTypeForm && continueQuestionType) {
                questionTypeForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const selectedType = document.querySelector('.question-type-radio:checked');

                    if (!selectedType) {
                        alert('Silakan pilih tipe soal terlebih dahulu');
                        return;
                    }

                    const formData = new FormData(this);
                    const actionUrl = this.action;

                    fetch(actionUrl, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                    'content')
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Replace modal content with the new form
                                const modal = document.getElementById('modalGlobal');
                                const content = document.getElementById('modalContent');

                                content.innerHTML = data.html;

                                // Initialize all components for the new form
                                setTimeout(() => {
                                    // Initialize Quill editors
                                    if (typeof window.initializeQuillEditors === 'function') {
                                        window.initializeQuillEditors();
                                    }

                                    // Initialize Dropzone
                                    if (typeof initDropzone === 'function') {
                                        initDropzone();
                                    }

                                    // Re-setup modal close handlers specifically for new content
                                    setupModalCloseHandlersForNewContent();

                                }, 500);

                            } else {
                                alert('Terjadi kesalahan: ' + data.message);
                            }
                        })
                        .catch(error => {
                            alert('Terjadi kesalahan saat memproses permintaan');
                        });
                });

                // Also handle button click directly as fallback
                continueQuestionType.addEventListener('click', function(e) {
                    e.preventDefault();

                    const selectedType = document.querySelector('.question-type-radio:checked');

                    if (!selectedType) {
                        alert('Silakan pilih tipe soal terlebih dahulu');
                        return;
                    }

                    const formData = new FormData();
                    formData.append('type', selectedType.value);
                    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute(
                        'content'));

                    // Construct the correct action URL
                    const materialId = '{{ $questionmaterial->id }}';
                    const actionUrl = questionTypeForm ? questionTypeForm.action :
                        "{{ route('question.detail.choose', $questionmaterial->id) }}";

                    fetch(actionUrl, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Replace modal content with the new form
                                const modal = document.getElementById('modalGlobal');
                                const content = document.getElementById('modalContent');

                                content.innerHTML = data.html;

                                // Initialize all components for the new form
                                setTimeout(() => {
                                    // Initialize Quill editors
                                    if (typeof window.initializeQuillEditors === 'function') {
                                        window.initializeQuillEditors();
                                    }

                                    // Initialize Dropzone
                                    if (typeof initDropzone === 'function') {
                                        initDropzone();
                                    }

                                    // Re-setup modal close handlers specifically for new content
                                    setupModalCloseHandlersForNewContent();

                                }, 500);

                            } else {
                                alert('Terjadi kesalahan: ' + data.message);
                            }
                        })
                        .catch(error => {
                            alert('Terjadi kesalahan saat memproses permintaan');
                        });
                });

            }
        }

        function setupModalCloseHandlers() {
            // Get modal reference
            const modal = document.getElementById('modalGlobal');

            // Setup close button handlers with valid CSS selectors
            const closeButtonSelectors = [
                '[onclick="closeModal()"]',
                '.modal-close',
                '#cancelQuestionType',
                '.close',
                '.btn-close',
                '[data-dismiss="modal"]',
                '[data-bs-dismiss="modal"]',
                'iconify-icon[icon="mdi:close"]',
                'iconify-icon[icon="material-symbols:close"]',
                'svg[class*="close"]',
                'button[class*="close"]',
                'button[aria-label="Close"]',
                'button[title="Close"]',
                'button[title="Tutup"]'
            ];

            closeButtonSelectors.forEach(selector => {
                const buttons = document.querySelectorAll(selector);

                buttons.forEach(button => {
                    // Remove existing onclick attribute to prevent conflicts
                    button.removeAttribute('onclick');

                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        closeModal();
                    });
                });
            });

            // Also check for any button that contains close-related text
            const allButtons = document.querySelectorAll('button');
            allButtons.forEach(button => {
                const buttonText = button.textContent.toLowerCase().trim();
                if (buttonText.includes('batal') || buttonText.includes('tutup') || buttonText.includes('close') ||
                    buttonText === '×') {
                    button.removeAttribute('onclick');
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        closeModal();
                    });
                }
            });

            // Setup overlay click to close
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeModal();
                    }
                });
            }

            // Setup ESC key to close
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeModal();
                }
            });
        }

        function setupModalCloseHandlersForNewContent() {
            // Focus specifically on modal content area
            const modalContent = document.getElementById('modalContent');
            if (!modalContent) {
                return;
            }

            // Find close buttons within the new modal content
            const closeButtonSelectors = [
                '[onclick="closeModal()"]',
                '[onclick="closeResponseModal()"]',
                '.modal-close',
                '.close',
                '.btn-close',
                '[data-dismiss="modal"]',
                '[data-bs-dismiss="modal"]',
                'button[aria-label="Close"]',
                'button[title="Close"]',
                'button[title="Tutup"]',
                'iconify-icon[icon*="close"]',
                'iconify-icon[icon*="x"]'
            ];

            closeButtonSelectors.forEach(selector => {
                const buttons = modalContent.querySelectorAll(selector);

                buttons.forEach(button => {
                    // Remove any existing onclick handlers
                    const originalOnclick = button.getAttribute('onclick');
                    button.removeAttribute('onclick');

                    // Add new event listener
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Check if this button originally called closeResponseModal
                        if (originalOnclick && originalOnclick.includes('closeResponseModal')) {
                            if (typeof closeResponseModal === 'function') {
                                closeResponseModal();
                            } else {
                                closeModal();
                            }
                        } else {
                            closeModal();
                        }
                    });
                });
            });

            // Also check for buttons with close-related text in new content
            const allButtons = modalContent.querySelectorAll('button');

            allButtons.forEach(button => {
                const buttonText = button.textContent.toLowerCase().trim();
                const buttonHTML = button.innerHTML.toLowerCase();

                if (buttonText.includes('batal') || buttonText.includes('tutup') || buttonText.includes('close') ||
                    buttonText === '×' || buttonHTML.includes('close') || buttonHTML.includes('×')) {

                    const originalOnclick = button.getAttribute('onclick');
                    button.removeAttribute('onclick');

                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Check if this button originally called closeResponseModal
                        if (originalOnclick && originalOnclick.includes('closeResponseModal')) {
                            if (typeof closeResponseModal === 'function') {
                                closeResponseModal();
                            } else {
                                closeModal();
                            }
                        } else {
                            closeModal();
                        }
                    });
                }
            });

            // Also check for iconify icons that might be close buttons
            const iconifyIcons = modalContent.querySelectorAll('iconify-icon');

            iconifyIcons.forEach(icon => {
                const iconName = icon.getAttribute('icon') || '';
                if (iconName.includes('close') || iconName.includes('x') || iconName.includes('cancel')) {
                    // Check if icon is clickable (has parent button or is clickable itself)
                    const clickableParent = icon.closest('button') || icon.closest('[onclick]') || icon.closest(
                        '.cursor-pointer');
                    if (clickableParent) {
                        clickableParent.removeAttribute('onclick');
                        clickableParent.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            closeModal();
                        });
                    }
                }
            });
        }

        function closeModal() {
            const modal = document.getElementById('modalGlobal');
            const content = document.getElementById('modalContent');

            content.classList.remove('scale-100', 'opacity-100');
            content.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                modal.classList.add('hidden');
                content.innerHTML = ''; // Optional: kosongin isi modal setelah tutup
                destroyDropzone();
            }, 200); // nunggu animasi selesai
        }

        function closeResponseModal() {
            const modal = document.getElementById('responseModal');
            const content = document.getElementById('responseContent');

            content.classList.remove('scale-100', 'opacity-100');
            content.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                modal.classList.add('hidden');
                content.innerHTML = ''; // Kosongkan modal
                destroyDropzone();
            }, 200);
        }

        let dz = null;

        function initDropzone(dropzoneSelector = '#myDropzone', inputSelector = '#uploadedImages') {
            if (dz) return; // Cegah duplikat inisialisasi

            Dropzone.autoDiscover = false;

            const uploadedImages = document.querySelector(inputSelector);
            const existingImages = uploadedImages?.value ? uploadedImages.value.split(',') : [];

            dz = new Dropzone(dropzoneSelector, {
                url: "{{ route('upload.image') }}",
                paramName: "file",
                acceptedFiles: "image/*",
                addRemoveLinks: true,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                init: function() {
                    const thisDropzone = this;

                    existingImages.forEach(function(filename) {
                        const mockFile = {
                            name: filename,
                            size: 123456,
                            type: 'image/jpeg'
                        };

                        mockFile.uploadedName = filename;

                        thisDropzone.emit("addedfile", mockFile);
                        thisDropzone.emit("thumbnail", mockFile, "/storage/uploads/images/question/" +
                            filename);
                        thisDropzone.emit("complete", mockFile);
                        thisDropzone.files.push(mockFile);
                    });
                },
                success: function(file, response) {
                    file.uploadedName = response.filename;

                    const current = uploadedImages.value ? uploadedImages.value.split(',') : [];
                    current.push(response.filename);
                    uploadedImages.value = current.join(',');
                },
                removedfile: function(file) {
                    file.previewElement.remove();

                    const filename = file.uploadedName || file.name;
                    const current = uploadedImages.value ? uploadedImages.value.split(',') : [];
                    const updated = current.filter(f => f !== filename);
                    uploadedImages.value = updated.join(',');
                }
            });
        }

        function destroyDropzone() {
            if (dz) {
                dz.destroy();
                dz = null;
            }
        }

        function toggleDropdown(event) {
            event.stopPropagation();

            // Tutup semua dropdown
            document.querySelectorAll('.dropdown-menu').forEach(el => el.classList.add('hidden'));

            // Toggle dropdown milik tombol yang ditekan
            const parent = event.currentTarget.closest('.relative');
            const dropdown = parent.querySelector('.dropdown-menu');
            if (dropdown) {
                dropdown.classList.toggle('hidden');
            }
        }

        // Tutup dropdown saat klik di luar
        document.addEventListener('click', () => {
            document.querySelectorAll('.dropdown-menu').forEach(el => el.classList.add('hidden'));
        });

        function toggleDropdown(event) {
            event.stopPropagation();

            // Tutup semua dropdown
            document.querySelectorAll('.dropdown-menu').forEach(el => el.classList.add('hidden'));

            // Toggle dropdown milik tombol yang ditekan
            const parent = event.currentTarget.closest('.relative');
            const dropdown = parent.querySelector('.dropdown-menu');
            if (dropdown) {
                dropdown.classList.toggle('hidden');
            }
        }

        // Tutup dropdown saat klik di luar
        document.addEventListener('click', () => {
            document.querySelectorAll('.dropdown-menu').forEach(el => el.classList.add('hidden'));
        });

        function confirmDelete(form) {
            Swal.fire({
                title: 'Yakin mau hapus?',
                text: "Soal yang dihapus tidak bisa dikembalikan!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    form.submit();
                }
            });
        }

        function openEditModal(event, url) {
            event.preventDefault();

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const modal = document.getElementById('modalGlobal');
                        const content = document.getElementById('modalContent');

                        content.innerHTML = data.html;
                        modal.classList.remove('hidden');
                        modal.classList.add('flex');

                        // Delay animasi biar smooth muncul
                        setTimeout(() => {
                            content.classList.remove('scale-95', 'opacity-0');
                            content.classList.add('scale-100', 'opacity-100');

                            initDropzone();

                            // Initialize Quill editors if they exist in the modal
                            if (typeof window.initializeQuillEditors === 'function') {
                                setTimeout(() => {
                                    window.initializeQuillEditors();
                                }, 500);
                            }

                            // Re-setup modal close handlers specifically for new content
                            setupModalCloseHandlersForNewContent();
                        }, 50);
                    } else {
                        console.error('Error:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Gagal membuka modal edit:', error);
                });
        }

        function openEditBankSoalModal(materialId) {
            const url = "{{ route('question.edit', ':id') }}".replace(':id', materialId);

            fetch(url)
                .then(response => response.text())
                .then(html => {
                    const modal = document.getElementById('modalGlobal');
                    const content = document.getElementById('modalContent');

                    content.innerHTML = html;
                    modal.classList.remove('hidden');

                    // Animasi muncul
                    setTimeout(() => {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }, 50);

                    // Setup modal listeners
                    setupModalListeners();
                })
                .catch(error => {
                    console.error('Gagal membuka modal edit bank soal:', error);
                });
        }

        // Show success/error messages
        @if (session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                timer: 3000,
                showConfirmButton: false
            });
        @endif

        @if (session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: '{{ session('error') }}',
                timer: 3000,
                showConfirmButton: false
            });
        @endif

        @if ($errors->any())
            let errorMessages = '';
            @foreach ($errors->all() as $error)
                errorMessages += '• {!! addslashes(str_replace(["\r\n", "\r", "\n"], "\\n", $error)) !!}\n';
            @endforeach

            Swal.fire({
                icon: 'error',
                title: 'Validasi Gagal!',
                html: '<div style="text-align: left; white-space: pre-line;">' + errorMessages + '</div>',
                timer: 8000,
                showConfirmButton: true,
                confirmButtonText: 'OK'
            });
        @endif

        // Search functionality for questions
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const questionItems = document.querySelectorAll('.question-item');
            let visibleCount = 0;

            questionItems.forEach(item => {
                const question = item.getAttribute('data-question') || '';
                const answers = item.getAttribute('data-answers') || '';

                if (question.includes(searchTerm) || answers.includes(searchTerm)) {
                    item.style.display = 'block';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });

            // Show/hide empty state message if needed
            const activeTabPane = document.querySelector('.tab-pane:not(.hidden)');
            if (activeTabPane) {
                const emptyState = activeTabPane.querySelector('.text-center.py-2');
                if (emptyState) {
                    if (visibleCount === 0 && searchTerm.length > 0) {
                        emptyState.style.display = 'block';
                        emptyState.innerHTML = `
                            <div class="mb-6">
                                <svg class="w-16 h-16 text-gray-300 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Tidak Ada Hasil</h3>
                            <p class="text-gray-500 mb-6">
                                Tidak ditemukan soal yang sesuai dengan pencarian "${searchTerm}".
                            </p>
                        `;
                    } else if (visibleCount > 0) {
                        emptyState.style.display = 'none';
                    }
                }
            }
        });
    </script>
@endsection
