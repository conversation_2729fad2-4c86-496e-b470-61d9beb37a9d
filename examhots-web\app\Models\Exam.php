<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Exam extends Model
{
    use HasFactory;

    protected $table = 'exam';

    protected $fillable = [
        'name',
        'startdate',
        'enddate',
        'starttime',
        'endtime',
        'duration',
        'token',
        'kkm',
        'amountquestion',
        'questionmaterialid',
        'trials',
        'classid',
        'teacher_id',
        'show_score',
    ];

    public function questionmaterial()
    {
        return $this->belongsTo(QuestionMaterial::class, 'questionmaterialid');
    }

    public function class()
    {
        return $this->belongsTo(StudentClass::class, 'classid');
    }

    public function examDetails()
    {
        return $this->hasMany(ExamDetail::class, 'examid');
    }

    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    /**
     * Parse time string to Carbon, handling both HH:MM and HH:MM:SS formats
     */
    private static function parseTime($timeString)
    {
        // Return null if timeString is null or empty
        if (empty($timeString) || $timeString === null) {
            return null;
        }

        // Try HH:MM:SS format first
        try {
            return Carbon::createFromFormat('H:i:s', $timeString);
        } catch (\Exception $e) {
            // Fall back to HH:MM format
            try {
                return Carbon::createFromFormat('H:i', $timeString);
            } catch (\Exception $e2) {
                // If both formats fail, return null
                return null;
            }
        }
    }

    /**
     * Check if there's a schedule conflict for a class
     *
     * @param int $classId
     * @param string $startDate
     * @param string $endDate
     * @param string $startTime
     * @param string $endTime
     * @param int|null $excludeExamId - ID ujian yang dikecualikan (untuk edit)
     * @return bool
     */
    public static function hasScheduleConflict($classId, $startDate, $endDate, $startTime, $endTime, $excludeExamId = null)
    {
        // If any of the new exam's schedule data is empty, no conflict check needed
        if (empty($startDate) || empty($endDate) || empty($startTime) || empty($endTime)) {
            return false;
        }

        $query = self::where('classid', $classId)
            // Only check exams that have complete schedule data
            ->whereNotNull('startdate')
            ->whereNotNull('enddate')
            ->whereNotNull('starttime')
            ->whereNotNull('endtime')
            ->where('startdate', '!=', '')
            ->where('enddate', '!=', '')
            ->where('starttime', '!=', '')
            ->where('endtime', '!=', '');

        // Exclude current exam if editing
        if ($excludeExamId) {
            $query->where('id', '!=', $excludeExamId);
        }

        // Check for date overlap
        $conflictingExams = $query->where(function ($q) use ($startDate, $endDate) {
            $q->where(function ($subQ) use ($startDate, $endDate) {
                // Case 1: New exam starts during existing exam
                $subQ->where('startdate', '<=', $startDate)
                    ->where('enddate', '>=', $startDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                // Case 2: New exam ends during existing exam
                $subQ->where('startdate', '<=', $endDate)
                    ->where('enddate', '>=', $endDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                // Case 3: New exam completely contains existing exam
                $subQ->where('startdate', '>=', $startDate)
                    ->where('enddate', '<=', $endDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                // Case 4: Existing exam completely contains new exam
                $subQ->where('startdate', '<=', $startDate)
                    ->where('enddate', '>=', $endDate);
            });
        })->get();

        // If no date conflicts, no time conflicts either
        if ($conflictingExams->isEmpty()) {
            return false;
        }

        // Check for time conflicts within overlapping dates
        foreach ($conflictingExams as $exam) {
            if (self::hasTimeConflict($exam, $startDate, $endDate, $startTime, $endTime)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if there's a time conflict between two exams
     */
    private static function hasTimeConflict($existingExam, $newStartDate, $newEndDate, $newStartTime, $newEndTime)
    {
        // Skip conflict check if either exam doesn't have complete schedule data
        if (
            empty($existingExam->startdate) || empty($existingExam->enddate) ||
            empty($existingExam->starttime) || empty($existingExam->endtime) ||
            empty($newStartDate) || empty($newEndDate) ||
            empty($newStartTime) || empty($newEndTime)
        ) {
            return false; // No conflict if any exam doesn't have complete schedule
        }

        // Convert times to Carbon for easier comparison
        // Handle both HH:MM and HH:MM:SS formats
        $existingStart = self::parseTime($existingExam->starttime);
        $existingEnd = self::parseTime($existingExam->endtime);
        $newStart = self::parseTime($newStartTime);
        $newEnd = self::parseTime($newEndTime);

        // If any time parsing failed, skip conflict check
        if (!$existingStart || !$existingEnd || !$newStart || !$newEnd) {
            return false;
        }

        // Check if dates overlap
        try {
            $existingStartDate = Carbon::parse($existingExam->startdate);
            $existingEndDate = Carbon::parse($existingExam->enddate);
            $newStartDateCarbon = Carbon::parse($newStartDate);
            $newEndDateCarbon = Carbon::parse($newEndDate);
        } catch (\Exception $e) {
            // If date parsing fails, skip conflict check
            return false;
        }

        // Find overlapping date range
        $overlapStart = $existingStartDate->max($newStartDateCarbon);
        $overlapEnd = $existingEndDate->min($newEndDateCarbon);

        // If there's no date overlap, no conflict
        if ($overlapStart->gt($overlapEnd)) {
            return false;
        }

        // Check time overlap within the overlapping dates
        // Time conflict occurs if:
        // 1. New exam starts during existing exam time
        // 2. New exam ends during existing exam time
        // 3. New exam completely contains existing exam time
        // 4. Existing exam completely contains new exam time

        return ($newStart->lt($existingEnd) && $newEnd->gt($existingStart));
    }

    /**
     * Get conflicting exams for a class and time period
     */
    public static function getConflictingExams($classId, $startDate, $endDate, $startTime, $endTime, $excludeExamId = null)
    {
        // If any of the schedule data is empty, return empty collection
        if (empty($startDate) || empty($endDate) || empty($startTime) || empty($endTime)) {
            return collect();
        }

        $query = self::with(['questionmaterial', 'class'])
            ->where('classid', $classId)
            // Only check exams that have complete schedule data
            ->whereNotNull('startdate')
            ->whereNotNull('enddate')
            ->whereNotNull('starttime')
            ->whereNotNull('endtime')
            ->where('startdate', '!=', '')
            ->where('enddate', '!=', '')
            ->where('starttime', '!=', '')
            ->where('endtime', '!=', '');

        if ($excludeExamId) {
            $query->where('id', '!=', $excludeExamId);
        }

        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->where(function ($subQ) use ($startDate, $endDate) {
                $subQ->where('startdate', '<=', $startDate)
                    ->where('enddate', '>=', $startDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                $subQ->where('startdate', '<=', $endDate)
                    ->where('enddate', '>=', $endDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                $subQ->where('startdate', '>=', $startDate)
                    ->where('enddate', '<=', $endDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                $subQ->where('startdate', '<=', $startDate)
                    ->where('enddate', '>=', $endDate);
            });
        })->get()->filter(function ($exam) use ($startDate, $endDate, $startTime, $endTime) {
            return self::hasTimeConflict($exam, $startDate, $endDate, $startTime, $endTime);
        });
    }
}
