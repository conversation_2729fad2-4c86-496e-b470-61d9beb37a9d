@extends('main')

@section('content')
    <!-- Page Header -->
    <div class="page-header flex items-center justify-between mb-8">
        <div class="flex items-center space-x-3">
            <svg class="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path
                    d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z">
                </path>
            </svg>
            <h1 class="page-title text-3xl font-bold text-gray-800">Bank Soal</h1>
        </div>

        <div class="page-header-actions flex items-center space-x-4">
            <!-- Search Bar -->
            <div class="search-container relative">
                <input type="text" id="searchInput" placeholder="Cari bank soal di sini..."
                    class="search-input w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent">
                <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>

            <!-- Add Button -->
            <a href="javascript:;" onclick="openCreateModal()"
                class="btn-primary bg-primary-blue text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 hover:shadow-lg hover:scale-105 transition-all duration-200 ease-in-out">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                <span>Buat Bank Soal Baru</span>
            </a>
        </div>
    </div>

    <!-- Cards Grid -->
    <div class="cards-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="cardsContainer">
        @forelse ($question as $questionItem)
            <div class="card-hover bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow question-card"
                data-name="{{ strtolower($questionItem->name) }}"
                data-description="{{ strtolower($questionItem->description) }}">

                <!-- Card Header -->
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 line-clamp-2">{{ $questionItem->name }}</h3>

                    <!-- Dropdown Menu -->
                    <div class="relative">
                        <button
                            class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors dropdown-toggle"
                            data-card-id="{{ $questionItem->id }}">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                                </path>
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div class="dropdown-menu card-dropdown absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 hidden"
                            data-card-id="{{ $questionItem->id }}">
                            <a href="{{ route('question.edit', $questionItem->id) }}"
                                onclick="openEditModal(event, '{{ route('question.edit', $questionItem->id) }}')"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:pencil" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Edit Bank Soal
                            </a>
                            <a href="{{ route('question.detail', $questionItem->id) }}"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:eye" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Detail Bank Soal
                            </a>
                            <hr class="border-gray-100 my-1">
                            <button type="button" onclick="confirmDelete({{ $questionItem->id }})"
                                class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                <iconify-icon icon="mdi:delete" width="16" height="16"
                                    class="mr-3 text-red-600"></iconify-icon>
                                Hapus Bank Soal
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                    {{ $questionItem->description ?? 'Tidak ada deskripsi tersedia.' }}
                </p>

                <!-- Teacher Info (Admin Only) -->
                @if (Auth::user()->role === 'admin' && $questionItem->teacher)
                    <div class="mb-3">
                        <span class="text-xs text-gray-500">Dibuat oleh:</span>
                        <span class="text-sm font-medium text-gray-700">{{ $questionItem->teacher->name }}</span>
                    </div>
                @endif

                <!-- Divider -->
                <hr class="border-gray-200 mb-4">

                <!-- Question Stats -->
                <div class="flex flex-wrap gap-2 mb-4">
                    @php
                        // Get question counts by type
                        $multipleChoice = \App\Models\Question::where('questionmaterialid', $questionItem->id)
                            ->where('type', 'pilihan_ganda')
                            ->count();
                        $shortAnswer = \App\Models\Question::where('questionmaterialid', $questionItem->id)
                            ->where('type', 'uraian_singkat')
                            ->count();
                        $essay = \App\Models\Question::where('questionmaterialid', $questionItem->id)
                            ->where('type', 'esai')
                            ->count();
                    @endphp

                    @if ($multipleChoice > 0)
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">{{ $multipleChoice }} Soal
                            PG</span>
                    @endif

                    @if ($shortAnswer > 0)
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">{{ $shortAnswer }} Soal Uraian
                            Singkat</span>
                    @endif

                    @if ($essay > 0)
                        <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">{{ $essay }} Soal
                            Essay</span>
                    @endif

                    @if ($multipleChoice == 0 && $shortAnswer == 0 && $essay == 0)
                        <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">Belum ada soal</span>
                    @endif
                </div>

                <!-- Hidden form for delete -->
                <form id="delete-form-{{ $questionItem->id }}" action="{{ route('question.delete', $questionItem->id) }}"
                    method="POST" class="hidden">
                    @csrf
                    @method('DELETE')
                </form>
            </div>
        @empty
            <!-- Empty State -->
            <div class="col-span-full flex flex-col items-center justify-center py-16">
                <svg class="w-24 h-24 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                    </path>
                </svg>
                <h3 class="text-xl font-semibold text-gray-500 mb-2">Belum ada bank soal</h3>
                <p class="text-gray-400 mb-6">Mulai dengan membuat bank soal pertama Anda</p>
                <button onclick="openCreateModal()"
                    class="bg-primary-blue text-white px-6 py-3 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 transition-all">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Buat Bank Soal Baru</span>
                </button>
            </div>
        @endforelse
    </div>

    <!-- No Results Message (Hidden by default) -->
    <div id="noResults" class="hidden col-span-full flex flex-col items-center justify-center py-16">
        <svg class="w-24 h-24 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        <h3 class="text-xl font-semibold text-gray-500 mb-2">Tidak ada hasil ditemukan</h3>
        <p class="text-gray-400">Coba gunakan kata kunci yang berbeda</p>
    </div>
@endsection

@section('script')
    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const cards = document.querySelectorAll('.question-card');
            const noResults = document.getElementById('noResults');
            let visibleCards = 0;

            cards.forEach(card => {
                const name = card.getAttribute('data-name');
                const description = card.getAttribute('data-description');

                if (name.includes(searchTerm) || description.includes(searchTerm)) {
                    card.style.display = 'block';
                    visibleCards++;
                } else {
                    card.style.display = 'none';
                }
            });

            // Show/hide no results message
            if (visibleCards === 0 && searchTerm !== '') {
                noResults.classList.remove('hidden');
            } else {
                noResults.classList.add('hidden');
            }
        });

        // Dropdown menu functionality
        document.addEventListener('click', function(e) {
            // Close all dropdowns first
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });

            // If clicked on dropdown toggle, open the corresponding dropdown
            if (e.target.closest('.dropdown-toggle')) {
                e.preventDefault();
                const button = e.target.closest('.dropdown-toggle');
                const cardId = button.getAttribute('data-card-id');
                const menu = document.querySelector(`.dropdown-menu[data-card-id="${cardId}"]`);

                if (menu) {
                    menu.classList.remove('hidden');
                }
            }
        });

        // Delete confirmation function
        function confirmDelete(id) {
            if (typeof Swal === 'undefined') {
                // Fallback to browser confirm
                if (confirm(
                        'Apakah Anda yakin ingin menghapus bank soal ini? Data yang dihapus tidak dapat dikembalikan!')) {
                    document.getElementById(`delete-form-${id}`).submit();
                }
                return;
            }

            Swal.fire({
                title: 'Apakah Anda yakin?',
                text: "Bank soal dan semua soal di dalamnya akan dihapus permanen!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#e3342f',
                cancelButtonColor: '#455A9D',
                confirmButtonText: 'Ya, hapus!',
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById(`delete-form-${id}`).submit();
                }
            });
        }

        // Add some CSS for line-clamp (text truncation)
        const style = document.createElement('style');
        style.textContent = `
            .line-clamp-2 {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
            .line-clamp-3 {
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
            .dropdown-menu {
                animation: slideDown 0.2s ease-out;
                transform-origin: top right;
            }
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px) scale(0.95);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
        `;
        document.head.appendChild(style);

        function openCreateModal() {
            fetch("{{ route('question.add') }}")
                .then(response => response.text())
                .then(html => {
                    const modal = document.getElementById('modalGlobal');
                    const content = document.getElementById('modalContent');

                    content.innerHTML = html;
                    modal.classList.remove('hidden');

                    // Biar animasinya muncul (delay dikit)
                    setTimeout(() => {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }, 50);

                    setupModalListeners();
                })
                .catch(error => {
                    console.error('Gagal membuka modal:', error);
                });
        }

        function openEditModal(event, url) {
            event.preventDefault(); // ✅ mencegah link langsung

            fetch(url)
                .then(response => response.text())
                .then(html => {
                    const modal = document.getElementById('modalGlobal');
                    const content = document.getElementById('modalContent');

                    content.innerHTML = html;
                    modal.classList.remove('hidden');

                    // Animasi muncul
                    setTimeout(() => {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }, 50);

                    // ✅ Pasang event listener untuk charCount, dll
                    setupModalListeners();
                })
                .catch(error => {
                    console.error('Gagal membuka modal edit:', error);
                });
        }


        function setupModalListeners() {
            // Initialize Quill editors if they exist in the modal
            console.log('Setting up modal listeners in index, checking for Quill editors...');
            if (typeof window.initializeQuillEditors === 'function') {
                setTimeout(() => {
                    console.log('Calling initializeQuillEditors from index setupModalListeners');
                    window.initializeQuillEditors();
                }, 500);
            } else {
                console.log('initializeQuillEditors function not found in index');
            }

            const textarea = document.getElementById('description');
            const charCount = document.getElementById('charCount');
            const nameField = document.getElementById('name');
            const saveBtn = document.getElementById('saveBtn');

            if (textarea && charCount) {
                textarea.addEventListener('input', () => {
                    const count = textarea.value.length;
                    charCount.textContent = `${count}/200`;

                    if (count > 200) {
                        charCount.classList.add('text-red-500');
                    } else {
                        charCount.classList.remove('text-red-500');
                    }
                });

                // Trigger awal: langsung update ketika modal dibuka
                charCount.textContent = `${textarea.value.length}/200`;
            }

            if (saveBtn && nameField) {
                saveBtn.addEventListener('click', function(e) {
                    const descriptionField = document.getElementById('description');

                    // Validate name field
                    if (nameField.value.trim() === '') {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Peringatan!',
                            text: 'Judul Bank Soal wajib diisi!',
                            confirmButtonColor: '#455A9D'
                        });
                        nameField.focus();
                        return;
                    }

                    // Validate description field
                    if (descriptionField && descriptionField.value.trim() === '') {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Peringatan!',
                            text: 'Deskripsi Bank Soal wajib diisi!',
                            confirmButtonColor: '#455A9D'
                        });
                        descriptionField.focus();
                        return;
                    }

                    // Validate description length
                    if (descriptionField && descriptionField.value.length > 200) {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Peringatan!',
                            text: 'Deskripsi Bank Soal tidak boleh lebih dari 200 karakter!',
                            confirmButtonColor: '#455A9D'
                        });
                        descriptionField.focus();
                        return;
                    }

                    // Admin validation for teacher selection
                    const teacherField = document.getElementById('teacher_id');
                    if (teacherField && teacherField.value.trim() === '') {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Peringatan!',
                            text: 'Pilih guru untuk bank soal ini!',
                            confirmButtonColor: '#455A9D'
                        });
                        teacherField.focus();
                        return;
                    }

                    // If validation passes, form will submit normally
                });
            }
        }

        function closeModal() {
            const modal = document.getElementById('modalGlobal');
            const content = document.getElementById('modalContent');

            content.classList.remove('scale-100', 'opacity-100');
            content.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                modal.classList.add('hidden');
                content.innerHTML = ''; // Optional: kosongin isi modal setelah tutup
            }, 200); // nunggu animasi selesai
        }

        // Show success/error messages
        @if (session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                timer: 3000,
                showConfirmButton: false
            });
        @endif

        @if (session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: '{{ session('error') }}',
                timer: 3000,
                showConfirmButton: false
            });
        @endif

        @if ($errors->any())
            let errorMessages = '';
            @foreach ($errors->all() as $error)
                errorMessages += '• {!! addslashes(str_replace(["\r\n", "\r", "\n"], "\\n", $error)) !!}\n';
            @endforeach

            Swal.fire({
                icon: 'error',
                title: 'Validasi Gagal!',
                html: '<div style="text-align: left; white-space: pre-line;">' + errorMessages + '</div>',
                timer: 8000,
                showConfirmButton: true,
                confirmButtonText: 'OK'
            });
        @endif
    </script>
@endsection
