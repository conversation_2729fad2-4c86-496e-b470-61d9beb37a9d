{{-- <!DOCTYPE html>
<html lang="en">

<head>
    <!--  Title -->
    <title>{{ $title ?? 'Modernize' }}</title>
    <!--  Required Meta Tag -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="handheldfriendly" content="true" />
    <meta name="MobileOptimized" content="width" />
    <meta name="description" content="Mordenize" />
    <meta name="author" content="" />
    <meta name="keywords" content="Mordenize" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--  Favicon -->
    <link rel="shortcut icon" type="image/png" href="{{ asset('package/dist/images/logos/favicon.png') }} " />
    <!-- Owl Carousel  -->
    <link rel="stylesheet"
        href="{{ asset('package/dist/libs/owl.carousel/package/dist/assets/owl.carousel.min.css') }}">

    <link rel="stylesheet" href="{{ asset('package/dist/libs/dropzone/dist/min/dropzone.min.css') }}">
    <!-- Core Css -->
    <link id="themeColors" rel="stylesheet" href="{{ asset('package/dist/css/style.min.css') }}" />

    <style>
        .dropzone .dz-preview .dz-image {
            border-radius: 8px;
            width: 120px;
            height: 120px;
            overflow: hidden;
        }

        .dropzone .dz-preview .dz-image img {
            object-fit: cover;
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden mt-2">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8">Detail Bank Soal</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Ujian</a></li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Bank Soal</a>
                                </li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Detail Bank
                                        Soal</a></li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Pilih Tipe
                                        Soal</a></li>
                                <li class="breadcrumb-item" aria-current="page">Pilihan Ganda</li>
                            </ol>
                        </nav>
                    </div>

                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                     
                        <form
                            action="{{ $mode === 'edit'
                                ? route('question.detail.edit.post', ['questionid' => $question->id, 'type' => $type])
                                : route('question.detail.add.type.post', ['materialid' => $material->id, 'type' => $type]) }}"
                            method="POST">
                            @csrf
                            @if ($mode === 'edit')
                                @method('PUT')
                            @endif
                     
                            <input type="hidden" name="uploaded_images" id="uploadedImages"
                                value="{{ $mode === 'edit' ? $question->img : '' }}">


                            <div id="myDropzone" class="dropzone mb-3"></div>

                            <div class="mb-3">
                                <label class="form-label" for="question_text">Pertanyaan</label><span
                                    class="text-danger">*</span>
                                <div id="question_editor" style="height: 200px;"></div>
                                <textarea name="question" id="question_text" style="display: none;">{{ old('question', $question->question ?? '') }}</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="answer" class="form-label">Jawaban</label><span
                                    class="text-danger">*</span>
                                @for ($i = 1; $i <= 5; $i++)
                                    @php
                                        $letter = chr(64 + $i);
                                    @endphp
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="correct"
                                            id="answer{{ $i }}" value="{{ $letter }}"
                                            @if (old('correct', isset($question->answers[$i - 1]) ? $question->answers[$i - 1]->is_correct : false)) checked @endif>
                                        <label class="form-check-label" for="answer{{ $i }}">
                                            {{ chr(64 + $i) }} <!-- A, B, C, D, E -->
                                        </label>

                                        <div id="answer_editor_{{ $i }}" style="height: 100px;" class="mt-2"></div>
                                        <textarea name="answer_{{ $i }}" id="answer_text_{{ $i }}" style="display: none;">{{ old("answer_$i", isset($question->answers[$i - 1]) ? $question->answers[$i - 1]->answer : '') }}</textarea>
                                    </div>
                                @endfor
                            </div>
                
                            <div class="d-flex align-items-center justify-content-end gap-2 mt-4">
                                <a href="{{ route('question.detail', $mode === 'edit' ? $question->questionmaterialid : $material->id) }}"
                                    class="btn btn-danger">
                                    <i class="ti ti-arrow-left"></i> Kembali
                                </a>
                                <button class="btn btn-primary">
                                    {{ $mode === 'edit' ? 'Simpan Perubahan' : 'Simpan' }}
                                    <i class="ti ti-send"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--  Customizer -->
    <!--  Import Js Files -->
    <script src="{{ asset('package/dist/libs/jquery/dist/jquery.min.js') }}"></script>
    <script src="{{ asset('package/dist/libs/simplebar/dist/simplebar.min.js') }}"></script>
    <script src="{{ asset('package/dist/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>
    <!--  core files -->
    <script src="{{ asset('package/dist/js/app.min.js') }}"></script>
    <script src="{{ asset('package/dist/js/app.init.js') }}"></script>
    <script src="{{ asset('package/dist/js/app-style-switcher.js') }}"></script>
    <script src="{{ asset('package/dist/js/sidebarmenu.js') }}"></script>
    <script src="{{ asset('package/dist/js/custom.js') }}"></script>
    <!--  current page js files -->
    <script src="{{ asset('package/dist/libs/owl.carousel/dist/owl.carousel.min.js') }}"></script>
    <script src="{{ asset('package/dist/libs/apexcharts/dist/apexcharts.min.js') }}"></script>
    <script src="{{ asset('package/dist/js/dashboard.js') }}"></script>

    <script src="{{ asset('package/dist/libs/dropzone/dist/min/dropzone.min.js') }}"></script>

    <script>
        Dropzone.autoDiscover = false;

        const uploadedImages = document.getElementById('uploadedImages');
        const existingImages = uploadedImages.value ? uploadedImages.value.split(',') : [];

        const dz = new Dropzone('#myDropzone', {
            url: "{{ route('upload.image') }}",
            paramName: "file",
            acceptedFiles: "image/*",
            addRemoveLinks: true,
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            init: function() {
                const thisDropzone = this;

                // Loop gambar lama (kalau ada)
                existingImages.forEach(function(filename) {
                    const mockFile = {
                        name: filename,
                        size: 123456,
                        type: 'image/jpeg'
                    };

                    // Penting! set juga file.uploadedName agar konsisten
                    mockFile.uploadedName = filename;

                    thisDropzone.emit("addedfile", mockFile);
                    thisDropzone.emit("thumbnail", mockFile, "/storage/uploads/images/question/" +
                        filename);
                    thisDropzone.emit("complete", mockFile);
                    thisDropzone.files.push(mockFile);
                });
            },
            success: function(file, response) {
                console.log('Upload sukses:', response);

                // simpan filename dari server ke objek Dropzone
                file.uploadedName = response.filename;

                // update hidden input
                const current = uploadedImages.value ? uploadedImages.value.split(',') : [];
                current.push(response.filename);
                uploadedImages.value = current.join(',');

                console.log('Updated hidden input:', uploadedImages.value);
            },
            error: function(file, response) {
                console.error('Upload error:', response);

                // Show error message
                let errorMessage = 'Upload gagal';
                if (typeof response === 'string') {
                    errorMessage = response;
                } else if (response && response.error) {
                    errorMessage = response.error;
                } else if (response && response.message) {
                    errorMessage = response.message;
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Upload Gagal!',
                    text: errorMessage,
                    confirmButtonColor: '#455A9D'
                });

                // Remove the file from dropzone
                this.removeFile(file);
            },
            removedfile: function(file) {
                file.previewElement.remove();

                const filename = file.uploadedName || file.name;
                console.log('Trying to remove:', filename);

                const current = uploadedImages.value ? uploadedImages.value.split(',') : [];
                const updated = current.filter(f => f !== filename);
                uploadedImages.value = updated.join(',');

                console.log('After removal:', uploadedImages.value);
            }
        });
    </script>

    @yield('script')
</body>

</html> --}}
<div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-6 rounded-t-2xl relative">
    <div class="flex items-center space-x-4">
        <!-- Question Mark Icon -->
        <div
            class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                </path>
            </svg>
        </div>
        <div>
            <h3 class="text-xl font-semibold text-gray-900">Pilihan Ganda</h3>
            <p class="text-gray-600 text-sm mt-1">Buat soal dan jawaban untuk melengkapi bank soal</p>
        </div>
    </div>
    <!-- Close Button -->
    <button id="closeQuestionTypeModal" onclick="closeResponseModal()" type="button"
        class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
            </path>
        </svg>
    </button>
</div>

<!-- Progress Bar -->
<div class="px-6 py-2">
    <div class="w-full bg-gray-200 rounded-full h-2">
        <div class="bg-primary-blue h-2 rounded-full w-full"></div>
    </div>
</div>

<form
    action="{{ $mode === 'edit'
        ? route('question.detail.edit.post', ['questionid' => $question->id, 'type' => $type])
        : route('question.detail.add.type.post', ['materialid' => $material->id, 'type' => $type]) }}"
    method="POST">
    @csrf
    @if ($mode === 'edit')
        @method('PUT')
    @endif
    <!-- Modal Body -->
    <div class="px-6 py-6 space-y-4">
        <input type="hidden" name="uploaded_images" id="uploadedImages"
            value="{{ $mode === 'edit' ? $question->img : '' }}">

        <div id="myDropzone" class="dropzone mb-3"></div>

        <div class="mb-3">
            <label for="question_text" class="block text-sm font-medium text-gray-700">
                Pertanyaan <span class="text-red-500">*</span>
            </label>
            <textarea name="question" id="question_text" placeholder="Masukkan Pertanyaan"
                class="mt-1 block w-full rounded-lg shadow-sm text-sm text-gray-900">{{ old('question', $question->question ?? '') }}</textarea>
        </div>

        <div class="mb-4">
            <label for="answer" class="block text-sm font-medium text-gray-700">
                Jawaban <span class="text-red-500">*</span>
            </label>

            @for ($i = 1; $i <= 5; $i++)
                @php
                    $letter = chr(64 + $i);
                @endphp
                <div class="flex items-start space-x-3 mb-3">
                    <input type="radio" name="correct" id="answer{{ $i }}" value="{{ $letter }}"
                        class="mt-2" @if (old('correct', isset($question->answers[$i - 1]) ? $question->answers[$i - 1]->is_correct : false)) checked @endif>

                    {{-- Label A/B/C bisa dihapus atau disembunyikan --}}
                    {{-- <label for="answer{{ $i }}" class="text-sm">{{ $letter }}</label> --}}

                    <textarea name="answer_{{ $i }}" id="answer_{{ $i }}"
                        placeholder="Isi jawaban pilihan {{ $letter }}"
                        class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm text-gray-900">{{ old("answer_$i", isset($question->answers[$i - 1]) ? $question->answers[$i - 1]->answer : '') }}</textarea>
                </div>
            @endfor
        </div>
    </div>

    <!-- Modal Footer -->
    <div class="px-6 py-6 bg-gray-50 rounded-b-2xl flex justify-end space-x-3">
        <button id="cancelQuestionType" onclick="closeResponseModal()" type="button"
            class="px-6 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
            Batal
        </button>
        <button id="continueQuestionType" type="submit"
            class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg disabled:opacity-50">
            {{ $mode === 'edit' ? 'Simpan Perubahan' : 'Simpan' }}
        </button>
    </div>
</form>

<!-- Quill.js CSS and JS -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Quill editor for question
        var quill = new Quill('#question_editor', {
            theme: 'snow',
            modules: {
                toolbar: [
                    [{
                        'header': [1, 2, 3, false]
                    }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{
                        'color': []
                    }, {
                        'background': []
                    }],
                    [{
                        'script': 'sub'
                    }, {
                        'script': 'super'
                    }],
                    [{
                        'list': 'ordered'
                    }, {
                        'list': 'bullet'
                    }],
                    [{
                        'indent': '-1'
                    }, {
                        'indent': '+1'
                    }],
                    [{
                        'align': []
                    }],
                    ['formula'],
                    ['clean']
                ]
            }
        });

        // Set initial content
        var initialContent = document.getElementById('question_text').value;
        if (initialContent) {
            quill.root.innerHTML = initialContent;
        }

        // Update hidden textarea when content changes
        quill.on('text-change', function() {
            document.getElementById('question_text').value = quill.root.innerHTML;
        });

        // Initialize Quill editors for answer options
        var answerQuills = [];
        for (let i = 1; i <= 5; i++) {
            (function(index) {
                var answerQuill = new Quill('#answer_editor_' + index, {
                    theme: 'snow',
                    modules: {
                        toolbar: [
                            ['bold', 'italic', 'underline'],
                            [{
                                'script': 'sub'
                            }, {
                                'script': 'super'
                            }],
                            ['formula'],
                            ['clean']
                        ]
                    }
                });

                // Set initial content for answer
                var initialAnswerContent = document.getElementById('answer_text_' + index).value;
                if (initialAnswerContent) {
                    answerQuill.root.innerHTML = initialAnswerContent;
                }

                // Update hidden textarea when answer content changes
                answerQuill.on('text-change', function() {
                    document.getElementById('answer_text_' + index).value = answerQuill.root
                        .innerHTML;
                });

                answerQuills[index - 1] = answerQuill;
            })(i);
        }

        // Update Quill when form is submitted
        document.querySelector('form').addEventListener('submit', function() {
            document.getElementById('question_text').value = quill.root.innerHTML;

            // Update all answer fields
            for (let i = 1; i <= 5; i++) {
                document.getElementById('answer_text_' + i).value = answerQuills[i - 1].root.innerHTML;
            }
        });
    });
</script>
