<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class StudentController extends Controller
{
    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $student = Student::select('student.*', 'class.name as classname', 'users.email')
            ->join('class', 'student.classid', '=', 'class.id')
            ->leftJoin('users', 'student.id', '=', 'users.studentid')
            ->orderBy('student.name', 'asc')
            ->get();

        return view('admin.management.student.index', [
            'title' => 'Murid',
            'student' => $student,
        ]);
    }

    public function add()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $classStudent = StudentClass::select('id', 'name')->orderBy('name', 'asc')->get();

        return view('admin.management.student.add', [
            'title' => 'Tambah Murid',
            'classStudent' => $classStudent,
        ]);
    }

    public function processAdd(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        try {
            $request->validate([
                'nisn' => 'nullable|string|unique:student,nisn',
                'nis' => 'nullable|string',
                'name' => 'required',
                'parentname' => 'nullable',
                'gender' => 'nullable|in:male,female',
                'phonenumber' => 'nullable|string|regex:/^[0-9+\-\s]+$/',
                'religion' => 'nullable',
                'email' => 'required|email|unique:users,email',
                'password' => 'nullable|min:6'
            ], [
                'nisn.string' => 'NISN harus berupa teks',
                'nisn.unique' => 'NISN sudah digunakan, silakan gunakan NISN lain',
                'nis.string' => 'NIS harus berupa teks',
                'name.required' => 'Nama wajib diisi',
                'gender.in' => 'Jenis kelamin tidak valid',
                'gender.in' => 'Jenis kelamin harus male atau female',
                'phonenumber.string' => 'Nomor telepon harus berupa teks',
                'phonenumber.regex' => 'Nomor telepon hanya boleh berisi angka, +, -, dan spasi',
                'email.required' => 'Email wajib diisi',
                'email.email' => 'Format email tidak valid',
                'email.unique' => 'Email sudah digunakan, silakan gunakan email lain',
                'password.min' => 'Password minimal 6 karakter'
            ]);

            $phonenumber = $request->phonenumber;
            if ($phonenumber && substr($phonenumber, 0, 2) === '08') {
                $phonenumber = '62' . substr($phonenumber, 2);
            }

            $student = Student::create([
                'nisn' => $request->nisn,
                'nis' => $request->nis,
                'classid' => $request->classid,
                'name' => $request->name,
                'parentname' => $request->parentname,
                'gender' => $request->gender,
                'phonenumber' => $phonenumber,
                'religion' => $request->religion,
                'address' => $request->address ?? null,
            ]);

            User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->filled('password') ? $request->password : '123456'),
                'role' => 'student',
                'studentid' => $student->id,
            ]);

            return redirect()->route('student.index')->with('success', 'Data murid berhasil ditambahkan');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            return redirect()->route('student.index')->with('error', 'Gagal menambahkan data murid: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $student = Student::with('user')->find($id);

        $classStudent = StudentClass::select('id', 'name')->orderBy('name', 'asc')->get();

        return view('admin.management.student.edit', [
            'title' => 'Edit Murid',
            'student' => $student,
            'classStudent' => $classStudent,
        ]);
    }

    public function processEdit(Request $request, $id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        try {
            $student = Student::with('user')->findOrFail($id);

            $request->validate([
                'nisn' => 'nullable|numeric|unique:student,nisn,' . $student->id,
                'nis' => 'nullable|numeric',
                'name' => 'required',
                'parentname' => 'nullable',
                'gender' => 'nullable|in:male,female',
                'phonenumber' => 'nullable|numeric',
                'religion' => 'nullable',
                // Email harus unik di tabel users kecuali milik user saat ini
                'email' => 'required|email|unique:users,email,' . $student->user->id,
                // Password boleh kosong, tapi kalau diisi harus minimal 6 karakter
                'password' => 'nullable|min:6',
            ], [
                'nisn.numeric' => 'NISN harus berupa angka',
                'nisn.unique' => 'NISN sudah digunakan, silakan gunakan NISN lain',
                'nis.numeric' => 'NIS harus berupa angka',
                'name.required' => 'Nama wajib diisi',
                'gender.in' => 'Jenis kelamin harus male atau female',
                'phonenumber.numeric' => 'Nomor telepon harus berupa angka',
                'email.required' => 'Email wajib diisi',
                'email.email' => 'Format email tidak valid',
                'email.unique' => 'Email sudah digunakan, silakan gunakan email lain',
                'password.min' => 'Password minimal 6 karakter'
            ]);

            // Format nomor HP
            $phonenumber = $request->phonenumber;
            if ($phonenumber && substr($phonenumber, 0, 2) === '08') {
                $phonenumber = '62' . substr($phonenumber, 2);
            }

            // Update data siswa
            $student->update([
                'nisn' => $request->nisn,
                'nis' => $request->nis,
                'classid' => $request->classid,
                'name' => $request->name,
                'parentname' => $request->parentname,
                'gender' => $request->gender,
                'phonenumber' => $phonenumber,
                'religion' => $request->religion,
                'address' => $request->address ?? null,
            ]);

            // Update akun login di tabel users
            if ($student->user) {
                $student->user->update([
                    'name' => $request->name,
                    'email' => $request->email,
                    'password' => $request->filled('password')
                        ? Hash::make($request->password)
                        : $student->user->password,
                ]);
            }

            return redirect()->route('student.index')->with('success', 'Data murid & akun berhasil diubah');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            return redirect()->route('student.index')->with('error', 'Gagal mengubah data murid: ' . $e->getMessage());
        }
    }


    public function delete($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        try {
            $student = Student::with('user')->findOrFail($id);

            // Hapus user yang terkait (jika ada)
            if ($student->user) {
                $student->user->delete();
            }

            // Hapus data siswa
            $student->delete();

            return redirect()->route('student.index')->with('success', 'Data murid & akun berhasil dihapus');
        } catch (\Exception $e) {
            return redirect()->route('student.index')->with('error', 'Gagal menghapus data murid: ' . $e->getMessage());
        }
    }

    public function downloadTemplate()
    {
        try {
            // Create a simple CSV content for the template
            $csvContent = "nama,email,nisn,nis,parentname,gender,phonenumber,religion,address\n";
            $csvContent .= "Contoh Nama,<EMAIL>,1234567890,123456,Nama Orang Tua,male,08123456789,Islam,Alamat Contoh\n";
            $csvContent .= ",,,,,,,,\n"; // Empty row for user input

            return response($csvContent, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="template_import_siswa.csv"',
            ]);
        } catch (\Exception $e) {
            return back()->with('error', 'Gagal mengunduh template: ' . $e->getMessage());
        }
    }

    public function import(Request $request)
    {
        try {
            $request->validate([
                'excel_file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB max
            ], [
                'excel_file.required' => 'File Excel wajib dipilih',
                'excel_file.file' => 'File yang dipilih tidak valid',
                'excel_file.mimes' => 'File harus berformat Excel (.xlsx, .xls) atau CSV',
                'excel_file.max' => 'Ukuran file maksimal 10MB',
            ]);

            $file = $request->file('excel_file');
            $extension = $file->getClientOriginalExtension();

            // Read file content
            $fileContent = file_get_contents($file->getRealPath());

            if ($extension === 'csv') {
                $rows = array_map('str_getcsv', explode("\n", $fileContent));
            } else {
                // For Excel files, we'll use a simple approach
                // In production, you might want to use PhpSpreadsheet
                return back()->with('error', 'Saat ini hanya mendukung file CSV. Silakan konversi file Excel ke CSV terlebih dahulu.');
            }

            if (empty($rows) || count($rows) < 2) {
                return back()->with('error', 'File kosong atau tidak memiliki data yang valid.');
            }

            // Get header row
            $headers = array_map('trim', $rows[0]);

            // Validate headers
            $requiredHeaders = ['nama', 'email'];
            foreach ($requiredHeaders as $required) {
                if (!in_array($required, $headers)) {
                    return back()->with('error', "Kolom '{$required}' wajib ada dalam file.");
                }
            }

            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            // Process data rows
            for ($i = 1; $i < count($rows); $i++) {
                $row = $rows[$i];

                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                try {
                    // Map row data to associative array
                    $data = [];
                    foreach ($headers as $index => $header) {
                        $data[$header] = isset($row[$index]) ? trim($row[$index]) : '';
                    }

                    // Validate required fields
                    if (empty($data['nama']) || empty($data['email'])) {
                        $errors[] = "Baris " . ($i + 1) . ": Nama dan email wajib diisi";
                        $errorCount++;
                        continue;
                    }

                    // Check if email already exists
                    if (User::where('email', $data['email'])->exists()) {
                        $errors[] = "Baris " . ($i + 1) . ": Email {$data['email']} sudah terdaftar";
                        $errorCount++;
                        continue;
                    }

                    // Check if NISN already exists (if provided)
                    if (!empty($data['nisn']) && Student::where('nisn', $data['nisn'])->exists()) {
                        $errors[] = "Baris " . ($i + 1) . ": NISN {$data['nisn']} sudah terdaftar";
                        $errorCount++;
                        continue;
                    }

                    // Format phone number
                    $phonenumber = $data['phonenumber'] ?? '';
                    if (!empty($phonenumber) && substr($phonenumber, 0, 2) === '08') {
                        $phonenumber = '62' . substr($phonenumber, 2);
                    }

                    // Create user
                    $user = User::create([
                        'name' => $data['nama'],
                        'email' => $data['email'],
                        'password' => Hash::make('password123'), // Default password
                        'role' => 'student',
                    ]);

                    // Create student
                    $student = Student::create([
                        'nisn' => $data['nisn'] ?: null,
                        'nis' => $data['nis'] ?: null,
                        'name' => $data['nama'],
                        'parentname' => $data['parentname'] ?: null,
                        'gender' => $data['gender'] ?: null,
                        'phonenumber' => $phonenumber ?: null,
                        'religion' => $data['religion'] ?: null,
                        'address' => $data['address'] ?: null,
                        'classid' => 1, // Default class, you might want to make this configurable
                    ]);

                    // Update user with student ID
                    $user->update(['studentid' => $student->id]);

                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "Baris " . ($i + 1) . ": " . $e->getMessage();
                    $errorCount++;
                }
            }

            // Prepare result message
            $message = "Import selesai. Berhasil: {$successCount} siswa";
            if ($errorCount > 0) {
                $message .= ", Gagal: {$errorCount} siswa";
                if (count($errors) > 0) {
                    $message .= ". Error: " . implode('; ', array_slice($errors, 0, 5));
                    if (count($errors) > 5) {
                        $message .= " dan " . (count($errors) - 5) . " error lainnya";
                    }
                }
            }

            if ($successCount > 0) {
                return redirect()->route('student.index')->with('success', $message);
            } else {
                return back()->with('error', $message);
            }
        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat import: ' . $e->getMessage());
        }
    }
}
